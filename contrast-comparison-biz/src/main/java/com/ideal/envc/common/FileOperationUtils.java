package com.ideal.envc.common;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.text.SimpleDateFormat;
import java.util.Collection;
import java.util.Date;

/**
 * 文件读写操作工具类
 * 提供文件的创建、读取、更新、删除等基础操作功能
 *
 * <AUTHOR>
 */
public class FileOperationUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(FileOperationUtils.class);
    
    /**
     * 文件系统目录分隔符
     */
    private static final String FILE_SEPARATOR = File.separator;
    
    /**
     * 时间戳格式
     */
    private static final String TIMESTAMP_FORMAT = "yyyyMMddHHmmssSSS";
    
    /**
     * JSON文件扩展名
     */
    private static final String JSON_EXTENSION = ".json";

    /**
     * 生成文件全路径
     * 规则：基础路径 + 目录符 + 流程ID + 目录符 + 流程ID_时间戳.json
     *
     * @param basePath 基础路径
     * @param flowId   流程ID
     * @return 文件绝对路径（含文件名）
     */
    public static String generateFilePath(String basePath, String flowId) {
        logger.debug("开始生成文件路径，基础路径：{}，流程ID：{}", basePath, flowId);
        
        if (StringUtils.isBlank(basePath)) {
            logger.error("基础路径不能为空");
            throw new IllegalArgumentException("基础路径不能为空");
        }
        
        if (StringUtils.isBlank(flowId)) {
            logger.error("流程ID不能为空");
            throw new IllegalArgumentException("流程ID不能为空");
        }
        
        try {
            /* 生成时间戳 */
            SimpleDateFormat sdf = new SimpleDateFormat(TIMESTAMP_FORMAT);
            String timestamp = sdf.format(new Date());
            
            /* 构建文件路径 */
            StringBuilder pathBuilder = new StringBuilder();
            pathBuilder.append(basePath);
            
            /* 确保基础路径以目录分隔符结尾 */
            if (!basePath.endsWith(FILE_SEPARATOR)) {
                pathBuilder.append(FILE_SEPARATOR);
            }
            
            /* 添加流程ID作为目录 */
            pathBuilder.append(flowId);
            pathBuilder.append(FILE_SEPARATOR);
            
            /* 添加文件名：流程ID_时间戳.json */
            pathBuilder.append(flowId);
            pathBuilder.append("_");
            pathBuilder.append(timestamp);
            pathBuilder.append(JSON_EXTENSION);
            
            String filePath = pathBuilder.toString();
            logger.info("文件路径生成成功：{}", filePath);
            
            return filePath;
            
        } catch (Exception e) {
            logger.error("生成文件路径失败，基础路径：{}，流程ID：{}", basePath, flowId, e);
            throw new RuntimeException("生成文件路径失败：" + e.getMessage(), e);
        }
    }

    /**
     * 创建文件并写入内容
     * 使用UTF-8编码写入文件
     *
     * @param content  文件内容
     * @param filePath 文件绝对路径（含文件名）
     * @return true-写入成功，false-写入失败
     */
    public static boolean createFile(String content, String filePath) {
        logger.debug("开始创建文件，文件路径：{}", filePath);
        
        if (StringUtils.isBlank(filePath)) {
            logger.error("文件路径不能为空");
            return false;
        }
        
        if (content == null) {
            logger.warn("文件内容为null，将写入空字符串");
            content = "";
        }
        
        try {
            Path path = Paths.get(filePath);
            
            /* 创建父目录（如果不存在） */
            Path parentDir = path.getParent();
            if (parentDir != null && !Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
                logger.info("创建父目录成功：{}", parentDir);
            }
            
            /* 写入文件内容 */
            Files.write(path, content.getBytes(StandardCharsets.UTF_8), 
                       StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
            
            logger.info("文件创建成功，路径：{}，内容长度：{}", filePath, content.length());
            return true;
            
        } catch (IOException e) {
            logger.error("创建文件失败，路径：{}", filePath, e);
            return false;
        } catch (Exception e) {
            logger.error("创建文件异常，路径：{}", filePath, e);
            return false;
        }
    }

    /**
     * 读取文件内容
     * 使用UTF-8编码读取文件
     *
     * @param filePath 文件绝对路径（含文件名）
     * @return 文件内容字符串，读取失败返回null
     */
    public static String readFile(String filePath) {
        logger.debug("开始读取文件，文件路径：{}", filePath);
        
        if (StringUtils.isBlank(filePath)) {
            logger.error("文件路径不能为空");
            return null;
        }
        
        try {
            Path path = Paths.get(filePath);
            
            /* 检查文件是否存在 */
            if (!Files.exists(path)) {
                logger.warn("文件不存在：{}", filePath);
                return null;
            }
            
            /* 检查是否为文件 */
            if (!Files.isRegularFile(path)) {
                logger.error("路径不是文件：{}", filePath);
                return null;
            }
            
            /* 读取文件内容 */
            byte[] bytes = Files.readAllBytes(path);
            String content = new String(bytes, StandardCharsets.UTF_8);
            
            logger.info("文件读取成功，路径：{}，内容长度：{}", filePath, content.length());
            return content;
            
        } catch (IOException e) {
            logger.error("读取文件失败，路径：{}", filePath, e);
            return null;
        } catch (Exception e) {
            logger.error("读取文件异常，路径：{}", filePath, e);
            return null;
        }
    }

    /**
     * 更新文件内容
     * 使用UTF-8编码写入文件，支持追加或替换模式
     *
     * @param content     文件内容
     * @param filePath    文件绝对路径（含文件名）
     * @param isReplace   是否替换原有内容，true-替换，false-追加，默认为true
     * @return true-更新成功，false-更新失败
     */
    public static boolean updateFile(String content, String filePath, boolean isReplace) {
        logger.debug("开始更新文件，文件路径：{}，替换模式：{}", filePath, isReplace);
        
        if (StringUtils.isBlank(filePath)) {
            logger.error("文件路径不能为空");
            return false;
        }
        
        if (content == null) {
            logger.warn("文件内容为null，将写入空字符串");
            content = "";
        }
        
        try {
            Path path = Paths.get(filePath);
            
            /* 创建父目录（如果不存在） */
            Path parentDir = path.getParent();
            if (parentDir != null && !Files.exists(parentDir)) {
                Files.createDirectories(parentDir);
                logger.info("创建父目录成功：{}", parentDir);
            }
            
            /* 根据模式选择写入方式 */
            if (isReplace) {
                /* 替换模式：覆盖原有内容 */
                Files.write(path, content.getBytes(StandardCharsets.UTF_8), 
                           StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
                logger.info("文件替换更新成功，路径：{}，内容长度：{}", filePath, content.length());
            } else {
                /* 追加模式：在原有内容后追加 */
                Files.write(path, content.getBytes(StandardCharsets.UTF_8), 
                           StandardOpenOption.CREATE, StandardOpenOption.APPEND);
                logger.info("文件追加更新成功，路径：{}，追加内容长度：{}", filePath, content.length());
            }
            
            return true;
            
        } catch (IOException e) {
            logger.error("更新文件失败，路径：{}，替换模式：{}", filePath, isReplace, e);
            return false;
        } catch (Exception e) {
            logger.error("更新文件异常，路径：{}，替换模式：{}", filePath, isReplace, e);
            return false;
        }
    }

    /**
     * 更新文件内容（默认替换模式）
     * 使用UTF-8编码写入文件，默认替换原有内容
     *
     * @param content  文件内容
     * @param filePath 文件绝对路径（含文件名）
     * @return true-更新成功，false-更新失败
     */
    public static boolean updateFile(String content, String filePath) {
        return updateFile(content, filePath, true);
    }

    /**
     * 删除文件
     *
     * @param filePath 文件绝对路径（含文件名）
     * @return true-删除成功，false-删除失败
     */
    public static boolean deleteFile(String filePath) {
        logger.debug("开始删除文件，文件路径：{}", filePath);
        
        if (StringUtils.isBlank(filePath)) {
            logger.error("文件路径不能为空");
            return false;
        }
        
        try {
            Path path = Paths.get(filePath);
            
            /* 检查文件是否存在 */
            if (!Files.exists(path)) {
                logger.warn("文件不存在，无需删除：{}", filePath);
                return true;
            }
            
            /* 检查是否为文件 */
            if (!Files.isRegularFile(path)) {
                logger.error("路径不是文件，无法删除：{}", filePath);
                return false;
            }
            
            /* 删除文件 */
            Files.delete(path);
            logger.info("文件删除成功：{}", filePath);
            return true;
            
        } catch (IOException e) {
            logger.error("删除文件失败，路径：{}", filePath, e);
            return false;
        } catch (Exception e) {
            logger.error("删除文件异常，路径：{}", filePath, e);
            return false;
        }
    }

    /**
     * 批量删除文件
     *
     * @param filePaths 文件绝对路径集合
     * @return true-全部删除成功，false-存在删除失败的文件
     */
    public static boolean batchDeleteFiles(Collection<String> filePaths) {
        logger.debug("开始批量删除文件，文件数量：{}", filePaths != null ? filePaths.size() : 0);
        
        if (filePaths == null || filePaths.isEmpty()) {
            logger.warn("文件路径集合为空，无需删除");
            return true;
        }
        
        boolean allSuccess = true;
        int successCount = 0;
        int failCount = 0;
        
        for (String filePath : filePaths) {
            if (deleteFile(filePath)) {
                successCount++;
            } else {
                failCount++;
                allSuccess = false;
            }
        }
        
        logger.info("批量删除文件完成，总数：{}，成功：{}，失败：{}", 
                   filePaths.size(), successCount, failCount);
        
        return allSuccess;
    }
}
