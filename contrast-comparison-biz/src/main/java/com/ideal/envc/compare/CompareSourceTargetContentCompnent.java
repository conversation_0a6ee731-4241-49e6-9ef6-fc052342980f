//package com.ideal.envc.compare;
//
//import com.alibaba.fastjson2.JSON;
//import difflib.ChangeDelta;
//import difflib.Chunk;
//import difflib.DeleteDelta;
//import difflib.DiffRow;
//import difflib.DiffRowGenerator;
//import difflib.DiffUtils;
//import difflib.InsertDelta;
//import difflib.Patch;
//
//import java.io.BufferedReader;
//import java.io.FileInputStream;
//import java.io.FileNotFoundException;
//import java.io.IOException;
//import java.io.InputStreamReader;
//import java.nio.charset.StandardCharsets;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.HashMap;
//import java.util.HashSet;
//import java.util.List;
//import java.util.Map;
//import java.util.Set;
//import java.util.concurrent.ConcurrentHashMap;
//import java.util.regex.Matcher;
//import java.util.regex.Pattern;
//
///**
// * 优化版本的文件内容比对组件
// * 主要优化：
// * 1. HTML模板优化 - 预定义模板，减少字符串拼接
// * 2. 算法优化 - O(n²) 改为 O(n) 的HashMap索引
// * 3. 内存优化 - 预分配容量，减少扩容开销
// * 4. 字符串处理优化 - 缓存和快速处理
// * 5. 分块处理 - 大文件分块处理策略
// *
// * <AUTHOR>
// */
//public class CompareSourceTargetContentCompnent {
//
//    // ==================== 常量定义 ====================
//
//    // 正则表达式模式（预编译）
////    private static final Pattern FORMAT_PATTERN = Pattern.compile("^.+?\\s+\\(size: .+\\)$");
//    private static final Pattern FORMAT_PATTERN = Pattern.compile(
//            "^.+\\s+\\(size:\\s+[\\d.]+\\s*[KMGT]?B?" +
//                    "(?:,\\s*permissions:\\s+[rwx-]+,\\s*MD5:\\s+[a-fA-F0-9]+)?\\)$"
//    );
//    private static final Pattern FILENAME_PATTERN = Pattern.compile("^(.*?)\\s+\\(size: ");
//
//    // 比对结果常量
//    public static final String RESULTEQUAL   = "EQUAL";
//    public static final String RESULTINSERT  = "INSERT";
//    public static final String RESULTDELETE  = "DELETE";
//    public static final String RESULTCHANGE  = "CHANGE";
//
//    // 输出键常量
//    public static final String COMPARERESULT = "compareResult";
//    public static final String CONTENTS      = "contents";
//    public static final String IGNORECONTENT = "ignoreContent";
//    public static final String ISCOMPARISON  = "iscomparison";
//    public static final String MUSTCONTENT   = "mustContent";
//    public static final String SOURCECONTENT = "sourceContent";
//    public static final String TARGETCONTENT = "targetContent";
//    public static final String WORKFLOWID    = "workFlowId";
//    public static final String SOURCETYPE    = "sourceActType";
//    public static final String TARGETTYPE    = "targetActType";
//    public static final String CALLFLOW      = "CallFlow";
//
//    // ==================== 性能优化常量 ====================
//
//    // HTML模板常量 - 预定义减少拼接
//    private static final String HTML_HEADER =
//        "<div class=\"comparison_space\">" +
//        "<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" class=\"comparison_tab\">" +
//        "<tr class=\"cp_title\"><td><span>Source</span></td><td width=\"5\"></td>" +
//        "<td class=\"cp_line\"></td><td width=\"5\"></td><td><span>Target</span></td></tr>";
//
//    private static final String HTML_FOOTER = "</table></div>";
//
//
//
//    private static final String CELL_TEMPLATE_BASE =
//        "<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr>%s<td><div class=\"cp_text\">%s</div></td>" +
//        "<td><div class=\"cp_cn\">%s</div></td></tr></table>";
//
//    // 性能优化参数
//    private static final int ESTIMATED_ROW_HTML_SIZE = 500; // 每行HTML预估大小
//    private static final int CHUNK_SIZE = 1000; // 分块处理大小
//    private static final int MAX_CACHE_SIZE = 10000; // 缓存最大大小
//
//    // ==================== 缓存机制 ====================
//
//    // 字符串替换缓存 - 线程安全
//    private static final Map<String, String> REPLACE_CACHE = new ConcurrentHashMap<>();
//
//    // HTML片段缓存
//    private static final Map<String, String> HTML_FRAGMENT_CACHE = new ConcurrentHashMap<>();
//
//    // ==================== 内部数据结构 ====================
//
//    /**
//     * 文件行信息封装类
//     */
//    private static class FileLineInfo {
//        final int index;
//        final String line;
//
//        FileLineInfo(int index, String line) {
//            this.index = index;
//            this.line = line;
//        }
//    }
//
//
//
//    // ==================== 主要比对方法 ====================
//
//    /**
//     * 优化版本的文件内容比对方法
//     * 主要优化点：
//     * 1. 预分配StringBuilder容量
//     * 2. 大文件分块处理
//     * 3. 使用优化的HTML生成
//     * 4. 单行超大文件特殊处理
//     *
//     * @param aLines 源文件行列表
//     * @param bLines 目标文件行列表
//     * @return 比对结果Map
//     */
//    public Map<String, Object> compare(List<String> aLines, List<String> bLines) {
//        return compare(aLines, bLines, false);
//    }
//
//    /**
//     * 带参数控制的文件内容比对方法
//     *
//     * @param aLines 源文件行列表
//     * @param bLines 目标文件行列表
//     * @param useIntelligentSegmentation 是否使用智能分段模式（对大内容进行裁剪处理）
//     * @return 比对结果Map
//     */
//    public Map<String, Object> compare(List<String> aLines, List<String> bLines, boolean useIntelligentSegmentation) {
//        // 参数验证
//        if (aLines == null || bLines == null) {
//            return createErrorResult("Input lines cannot be null");
//        }
//
//        // 行数限制检查
//        if (aLines.size() > 10000) {
//            return createErrorResult("The number of lines in the source file exceeds 10,000!!!");
//        }
//        if (bLines.size() > 10000) {
//            return createErrorResult("The number of lines in the target file exceeds 10,000!!!");
//        }
//
//        // 检测大内容文件场景
//        if (isSingleLineLargeFile(aLines, bLines)) {
//            return compareLargeContentFile(aLines, bLines, useIntelligentSegmentation);
//        }
//
//        // 直接使用优化版本处理，不进行分块（避免行号不连续问题）
//        return compareOptimized(aLines, bLines);
//    }
//
//    /**
//     * 检测是否为包含大量内容的少行文件
//     * 这种情况下每行可能包含大量文本（包含\n换行符）
//     */
//    private boolean isSingleLineLargeFile(List<String> aLines, List<String> bLines) {
//        // 如果行数很少但内容很大，认为是大内容文件
//        if (aLines.size() <= 5 && bLines.size() <= 5) {
//            int totalLength = 0;
//            for (String line : aLines) {
//                totalLength += line != null ? line.length() : 0;
//            }
//            for (String line : bLines) {
//                totalLength += line != null ? line.length() : 0;
//            }
//            // 如果平均每行超过100KB，认为是大内容文件
//            return totalLength > (aLines.size() + bLines.size()) * 100 * 1024;
//        }
//        return false;
//    }
//
//    /**
//     * 检测内容是否完全相同
//     */
//    private boolean areContentsSame(List<String> aLines, List<String> bLines) {
//        if (aLines.size() != bLines.size()) {
//            return false;
//        }
//        for (int i = 0; i < aLines.size(); i++) {
//            if (!java.util.Objects.equals(aLines.get(i), bLines.get(i))) {
//                return false;
//            }
//        }
//        return true;
//    }
//
//    /**
//     * 创建错误结果
//     */
//    private Map<String, Object> createErrorResult(String errorMessage) {
//        Map<String, Object> outputs = new HashMap<>();
//        outputs.put(COMPARERESULT, errorMessage);
//        outputs.put("ret", false);
//        return outputs;
//    }
//
//    /**
//     * 判断是否需要分块处理
//     */
//    private boolean shouldUseChunking(List<String> aLines, List<String> bLines) {
//        int totalLines = aLines.size() + bLines.size();
//        return totalLines > CHUNK_SIZE * 2; // 总行数超过2000行时使用分块
//    }
//
//    /**
//     * 大内容文件专用比对方法 - 改进版
//     * 支持相同和不同内容的大文件，提供更好的性能和用户体验
//     */
//    private Map<String, Object> compareLargeContentFile(List<String> aLines, List<String> bLines, boolean useIntelligentSegmentation) {
//        Map<String, Object> outputs = new HashMap<>();
//
//        // 快速相等性检查
//        if (areContentsSame(aLines, bLines)) {
//            // 完全相同的大内容 - 使用优化的显示方式（不截断）
//            String optimizedHtml = generateOptimizedEqualHtml(aLines, bLines);
//            outputs.put(COMPARERESULT, optimizedHtml);
//            outputs.put("ret", true);
//            return outputs;
//        }
//
//        // 内容不同的大文件处理
//        if (useIntelligentSegmentation) {
//            // 使用智能分段比对（可能会裁剪内容）
//            return compareWithIntelligentSegmentation(aLines, bLines);
//        } else {
//            // 使用标准比对（保持完整内容）
//            return compareOptimized(aLines, bLines);
//        }
//    }
//
//    /**
//     * 为大内容相同文件生成优化HTML - 改进版
//     * 不截断内容，保持完整的比对效果
//     */
//    private String generateOptimizedEqualHtml(List<String> aLines, List<String> bLines) {
//        StringBuilder html = new StringBuilder(8192); // 预分配更大容量
//        html.append("<div class=\"comparison_space\">"
//                + "<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" class=\"comparison_tab\">"
//                + "<tr class=\"cp_title\"><td><span>Source</span></td><td width=\"5\"></td>"
//                + "<td class=\"cp_line\"></td><td width=\"5\"></td><td><span>Target</span></td></tr>");
//
//        for (int i = 0; i < aLines.size(); i++) {
//            String line = aLines.get(i);
//
//            // 对于大内容，使用智能显示策略
//            if (line != null && line.contains("\n")) {
//                // 将内容按换行符分割进行逐行显示
//                String[] subLines = line.split("\n");
//
//                // 为每个子行生成HTML，保持完整内容
//                for (int j = 0; j < subLines.length; j++) {
//                    // 生成唯一行号
//                    int lineNumber = (i * 1000) + j + 1;
//                    String subLine = getOptimizedReplace(subLines[j]);
//                    generateOriginalStyleHtml(RESULTEQUAL, html,
//                        String.valueOf(lineNumber), subLine,
//                        String.valueOf(lineNumber), subLine);
//                }
//            } else {
//                // 普通行处理 - 不截断，保持完整内容
//                String displayLine = getOptimizedReplace(line != null ? line : "");
//                generateOriginalStyleHtml(RESULTEQUAL, html,
//                    String.valueOf(i + 1), displayLine,
//                    String.valueOf(i + 1), displayLine);
//            }
//        }
//
//        html.append("</table></div>");
//        return html.toString();
//    }
//
//    /**
//     * 生成内容摘要信息
//     */
//    private String generateContentSummary(String fullContent, int lineCount) {
//        if (fullContent == null || fullContent.isEmpty()) {
//            return "[空内容]";
//        }
//
//        // 生成摘要：显示前200字符 + 统计信息
//        String preview = fullContent.length() > 200 ?
//            fullContent.substring(0, 200) + "..." : fullContent;
//
//        // 移除换行符用于显示
//        preview = preview.replace("\n", " ").replace("\r", " ");
//
//        return String.format("<strong>[大内容文件 - 总长度: %d 字符, 包含 %d 行]</strong><br/>%s",
//            fullContent.length(),
//            lineCount,
//            getOptimizedReplace(preview));
//    }
//
//    /**
//     * 智能分段比对方法 - 改进版
//     * 对大内容文件进行智能分段处理，平衡性能和准确性
//     * 注意：此方法可能会对内容进行裁剪，但输出格式保持与原版一致
//     */
//    private Map<String, Object> compareWithIntelligentSegmentation(List<String> aLines, List<String> bLines) {
//        // 策略：将大内容按行分割，然后进行逐行比对
//        List<String> expandedALines = expandLargeContentToLines(aLines);
//        List<String> expandedBLines = expandLargeContentToLines(bLines);
//
//        // 如果展开后的行数仍然很大，进行采样处理
//        if (expandedALines.size() > 5000 || expandedBLines.size() > 5000) {
//            // 采样策略：每N行取1行进行比对
//            int sampleRate = Math.max(10, Math.max(expandedALines.size(), expandedBLines.size()) / 1000);
//
//            List<String> sampledA = sampleLines(expandedALines, sampleRate);
//            List<String> sampledB = sampleLines(expandedBLines, sampleRate);
//
//            // 对采样数据进行比对，输出格式与原版完全一致
//            return compareOptimized(sampledA, sampledB);
//        }
//
//        // 否则使用正常的优化比对
//        return compareOptimized(expandedALines, expandedBLines);
//    }
//
//    /**
//     * 将大内容展开为行列表
//     * 将包含换行符的大内容分割成独立的行
//     */
//    private List<String> expandLargeContentToLines(List<String> originalLines) {
//        List<String> expandedLines = new ArrayList<>();
//
//        for (String line : originalLines) {
//            if (line != null && line.contains("\n")) {
//                // 分割包含换行符的行
//                String[] subLines = line.split("\n", -1); // -1保留空字符串
//                for (String subLine : subLines) {
//                    expandedLines.add(subLine);
//                }
//            } else {
//                expandedLines.add(line);
//            }
//        }
//
//        return expandedLines;
//    }
//
//    /**
//     * 对行列表进行采样
//     * 按指定的采样率提取关键行，用于大文件的快速处理
//     */
//    private List<String> sampleLines(List<String> lines, int sampleRate) {
//        List<String> sampledLines = new ArrayList<>();
//
//        for (int i = 0; i < lines.size(); i += sampleRate) {
//            sampledLines.add(lines.get(i));
//        }
//
//        // 确保最后一行也被包含（如果不是正好被采样到）
//        if (lines.size() > 0 && (lines.size() - 1) % sampleRate != 0) {
//            sampledLines.add(lines.get(lines.size() - 1));
//        }
//
//        return sampledLines;
//    }
//
//    /**
//     * 生成差异摘要信息
//     */
//    private String generateDifferencesSummary(String currentLine, String otherLine, String fileType) {
//        if (currentLine == null || currentLine.isEmpty()) {
//            return "[空内容]";
//        }
//
//        // 计算基本统计信息
//        int currentLength = currentLine.length();
//        int otherLength = otherLine != null ? otherLine.length() : 0;
//        int currentLines = currentLine.split("\n").length;
//        int otherLines = otherLine != null ? otherLine.split("\n").length : 0;
//
//        // 生成预览内容
//        String preview = currentLine.length() > 300 ?
//            currentLine.substring(0, 300) + "..." : currentLine;
//        preview = preview.replace("\n", " ").replace("\r", " ");
//
//        // 生成差异摘要
//        StringBuilder summary = new StringBuilder();
//        summary.append(String.format("<strong>[%s差异摘要]</strong><br/>", fileType));
//        summary.append(String.format("长度: %d 字符 (对比: %d)<br/>", currentLength, otherLength));
//        summary.append(String.format("行数: %d 行 (对比: %d)<br/>", currentLines, otherLines));
//
//        if (currentLength != otherLength) {
//            summary.append(String.format("长度差异: %+d 字符<br/>", currentLength - otherLength));
//        }
//        if (currentLines != otherLines) {
//            summary.append(String.format("行数差异: %+d 行<br/>", currentLines - otherLines));
//        }
//
//        summary.append("内容预览: ").append(getOptimizedReplace(preview));
//
//        return summary.toString();
//    }
//    /**
//     * 优化版本的比对处理（适用于中小文件）
//     * 保持与原始逻辑完全一致的处理流程
//     */
//    private Map<String, Object> compareOptimized(List<String> aLines, List<String> bLines) {
//        Map<String, Object> outputs = new HashMap<>();
//        // 与原始版本保持一致：初始值为false
//        boolean ret = true;
//
//        // 预分配StringBuilder容量 - 性能优化关键点1
//        int estimatedSize = (aLines.size() + bLines.size()) * ESTIMATED_ROW_HTML_SIZE;
//        StringBuilder compareResult = new StringBuilder(estimatedSize);
//
//        // 添加HTML头部 - 与原始版本完全一致的HTML结构
//        compareResult.append("<div class=\"comparison_space\">"
//                + "<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\" class=\"comparison_tab\">"
//                + "<tr class=\"cp_title\"><td><span>Source</span></td><td width=\"5\"></td>"
//                + "<td class=\"cp_line\"></td><td width=\"5\"></td><td><span>Target</span></td></tr>");
//
//        // 生成差异补丁
//        DiffRowGenerator.Builder builder = new DiffRowGenerator.Builder();
//        DiffRowGenerator dfg = builder.build();
//
//        // 根据格式选择合适的Patch算法 - 性能优化关键点2
//        Patch<String> patch;
//        boolean isFormatValid = isAllLinesValidFormat(aLines) && isAllLinesValidFormat(bLines);
//        if (isFormatValid) {
//            // 使用优化算法
//            patch = generateOptimizedFileNamePatch(aLines, bLines);
//        } else {
//            patch = DiffUtils.diff(aLines, bLines);
//        }
//
//        List<DiffRow> rows = dfg.generateDiffRows(aLines, bLines, patch);
//        // 处理差异行 - 性能优化关键点3：使用优化的HTML生成
//        int rowsSize = rows.size();
//        int insertSize = 0;
//        int deleteSize = 0;
//        int oldSize = aLines.size();
//        int newSize = bLines.size();
//
//        for (int i = 0; i < rowsSize; i++) {
//            DiffRow diffRow = rows.get(i);
//            String tag = diffRow.getTag().toString();
//            DiffRow.Tag tag1 = diffRow.getTag();
//
//            // 优化字符串处理 - 性能优化关键点4
//            String oldLine = getOptimizedReplace(diffRow.getOldLine());
//            String newLine = getOptimizedReplace(diffRow.getNewLine());
//
//            String oldNum = String.valueOf(i + 1 - insertSize);
//            String newNum = String.valueOf(i + 1 - deleteSize);
//
//            // 处理不同类型的差异
//            if ((i + 1 - insertSize) > oldSize) {
//                ret = false;
//                tag1 = DiffRow.Tag.INSERT;
//                tag = RESULTINSERT;
//                insertSize++;
//                oldNum = "";
//                newNum = String.valueOf(i + 1 - deleteSize);
//            } else if ((i + 1 - deleteSize) > newSize) {
//                ret = false;
//                tag1 = DiffRow.Tag.DELETE;
//                tag = RESULTDELETE;
//                deleteSize++;
//                oldNum = String.valueOf(i + 1 - insertSize);
//                newNum = "";
//            } else if (RESULTCHANGE.equals(tag)) {
//                ret = false;
//                if ((i + 1 - insertSize) > oldSize || (oldLine != null && oldLine.length() == 0
//                        && !oldLine.equals(aLines.get(i - insertSize)))) {
//                    tag1 = DiffRow.Tag.INSERT;
//                    tag = RESULTINSERT;
//                    insertSize++;
//                    oldNum = "";
//                    newNum = String.valueOf(i + 1 - deleteSize);
//                } else if ((i + 1 - deleteSize) > newSize
//                        || newLine != null && newLine.length() == 0 && !newLine.equals(bLines.get(i - deleteSize))) {
//                    tag1 = DiffRow.Tag.DELETE;
//                    tag = RESULTDELETE;
//                    deleteSize++;
//                    oldNum = String.valueOf(i + 1 - insertSize);
//                    newNum = "";
//                }
//            } else if (RESULTINSERT.equals(tag)) {
//                insertSize++;
//                ret = false;
//                oldNum = "";
//                newNum = String.valueOf(i + 1 - deleteSize);
//            } else if (RESULTDELETE.equals(tag)) {
//                deleteSize++;
//                ret = false;
//                oldNum = String.valueOf(i + 1 - insertSize);
//                newNum = "";
//            }
//            diffRow.setTag(tag1);
//
//            // 使用优化的HTML生成方法 - 性能优化关键点5
//            appendOptimizedHtmlRow(tag, compareResult, oldNum, oldLine, newNum, newLine);
//        }
//
//        // 添加HTML尾部
//        compareResult.append("</table></div>");
//
//        // 构建返回结果 - 与原始版本完全一致，不添加额外字段
//        outputs.put(COMPARERESULT, compareResult.toString());
//        outputs.put("ret", ret);
//        return outputs;
//    }
//
//    // ==================== 分块处理方法 ====================
//
//    /**
//     * 大文件分块处理方法 - 已禁用分块以保持行号连续性
//     * 直接使用优化版本处理，避免行号不连续的问题
//     */
//    private Map<String, Object> compareWithChunking(List<String> aLines, List<String> bLines) {
//        // 不再进行分块处理，直接使用优化版本
//        // 因为分块会导致行号重新从1开始，破坏原版的行号连续性
//        return compareOptimized(aLines, bLines);
//    }
//
//
//
//    // ==================== 优化的HTML生成方法 ====================
//
//    /**
//     * 优化版本的HTML行生成方法
//     * 完全按照原始版本的逻辑生成HTML，确保结果一致
//     */
//    private void appendOptimizedHtmlRow(String tag, StringBuilder compareResult,
//                                      String oldNum, String oldLine, String newNum, String newLine) {
//        // 直接调用与原始版本完全一致的HTML生成逻辑
//        generateOriginalStyleHtml(tag, compareResult, oldNum, oldLine, newNum, newLine);
//    }
//
//    // ==================== HTML片段常量 - 性能优化 ====================
//
//    // 预定义HTML片段常量，避免重复创建
//    private static final String ROW_SEPARATOR_NORMAL = "<tr height=\"5\"><td></td><td width=\"5\"></td><td class=\"cp_line\"></td><td width=\"5\"></td><td></td></tr><tr>";
//    private static final String ROW_SEPARATOR_EQUAL = "<tr height=\"5\" class=\"cps_tr1\"><td></td><td width=\"5\"></td><td class=\"cp_line\"></td><td width=\"5\"></td><td></td></tr>";
//    private static final String TABLE_START = "<table cellpadding=\"0\" cellspacing=\"0\" border=\"0\"><tr>";
//    private static final String CELL_DIVIDER = "</div></td><td><div class=\"cp_cn\">";
//    private static final String CELL_END_LINE = "</div></td></tr></table></td><td width=\"5\"></td><td class=\"cp_line\"></td>";
//    private static final String PLACEHOLDER_CELL = "<td><div class=\"placeholder\"></div></td><td><div class=\"cp_text\">";
//    private static final String ROW_END = "</div></td></tr></table></td></tr>";
//
//    // 图标HTML片段
//    private static final String ICON_DELETE = "<td><div class=\"cp_icon icon_pos\"></div></td><td><div class=\"cp_text\">";
//    private static final String ICON_INSERT = "<td><div class=\"cp_icon icon_pos2\"></div></td><td><div class=\"cp_text\">";
//    private static final String ICON_CHANGE = "<td><div class=\"cp_icon icon_pos3\"></div></td><td><div class=\"cp_text\">";
//
//    // 单元格框架
//    private static final String FRAME_ABNORMAL = "<td class=\"cp_frame abnormal cpi_td_w\">";
//    private static final String FRAME_COMPLETE = "<td class=\"cp_frame complete cpi_td_w\">";
//    private static final String FRAME_WARNING = "<td class=\"cp_frame warning cpi_td_w\">";
//    private static final String FRAME_NORMAL = "<td class=\"cp_frame cpi_td_w\">";
//    private static final String FRAME_SEPARATOR = "<td width=\"5\"></td>";
//
//    /**
//     * 高性能版本的HTML生成方法
//     * 使用预定义常量和直接append，避免字符串拼接开销
//     */
//    private void generateOriginalStyleHtml(String tag, StringBuilder compareResult, String oldNum, String oldLine, String newNum, String newLine) {
//        if (RESULTDELETE.equals(tag)) {
//            // 删除行：红色异常框 + 普通框
//            compareResult.append(ROW_SEPARATOR_NORMAL)
//                        .append(FRAME_ABNORMAL)
//                        .append(TABLE_START)
//                        .append(ICON_DELETE)
//                        .append(oldNum)
//                        .append(CELL_DIVIDER)
//                        .append(oldLine)
//                        .append(CELL_END_LINE)
//                        .append(FRAME_SEPARATOR)
//                        .append(FRAME_NORMAL)
//                        .append(TABLE_START)
//                        .append(PLACEHOLDER_CELL)
//                        .append(newNum)
//                        .append(CELL_DIVIDER)
//                        .append(newLine);
//        } else if (RESULTINSERT.equals(tag)) {
//            // 插入行：普通框 + 绿色完成框
//            compareResult.append(ROW_SEPARATOR_NORMAL)
//                        .append(FRAME_NORMAL)
//                        .append(TABLE_START)
//                        .append(PLACEHOLDER_CELL)
//                        .append(oldNum)
//                        .append(CELL_DIVIDER)
//                        .append(oldLine)
//                        .append(CELL_END_LINE)
//                        .append(FRAME_SEPARATOR)
//                        .append(FRAME_COMPLETE)
//                        .append(TABLE_START)
//                        .append(ICON_INSERT)
//                        .append(newNum)
//                        .append(CELL_DIVIDER)
//                        .append(newLine);
//        } else if (RESULTCHANGE.equals(tag)) {
//            // 修改行：黄色警告框 + 黄色警告框
//            compareResult.append(ROW_SEPARATOR_NORMAL)
//                        .append(FRAME_WARNING)
//                        .append(TABLE_START)
//                        .append(ICON_CHANGE)
//                        .append(oldNum)
//                        .append(CELL_DIVIDER)
//                        .append(oldLine)
//                        .append(CELL_END_LINE)
//                        .append(FRAME_SEPARATOR)
//                        .append(FRAME_WARNING)
//                        .append(TABLE_START)
//                        .append(ICON_CHANGE)
//                        .append(newNum)
//                        .append(CELL_DIVIDER)
//                        .append(newLine);
//        } else {
//            // 相等行：特殊分隔符 + 普通框 + 普通框
//            compareResult.append(ROW_SEPARATOR_EQUAL)
//                        .append("<tr class=\"cps_tr2\">")
//                        .append(FRAME_NORMAL)
//                        .append(TABLE_START)
//                        .append(PLACEHOLDER_CELL)
//                        .append(oldNum)
//                        .append(CELL_DIVIDER)
//                        .append(oldLine)
//                        .append(CELL_END_LINE)
//                        .append(FRAME_SEPARATOR)
//                        .append(FRAME_NORMAL)
//                        .append(TABLE_START)
//                        .append(PLACEHOLDER_CELL)
//                        .append(newNum)
//                        .append(CELL_DIVIDER)
//                        .append(newLine);
//        }
//        compareResult.append(ROW_END);
//    }
//
//
//
//    /**
//     * 保留原有的方法以保持兼容性（已废弃，建议使用appendOptimizedHtmlRow）
//     * @deprecated 使用 appendOptimizedHtmlRow 替代
//     */
//    @Deprecated
//    public void initResultStr(String tag, StringBuilder compareResult, String oldNum, String oldLine, String newNum,
//                             String newLine) {
//        appendOptimizedHtmlRow(tag, compareResult, oldNum, oldLine, newNum, newLine);
//    }
//
//    // ==================== 优化的文件名Patch算法 ====================
//
//    /**
//     * 优化版本的文件名Patch生成算法
//     * 使用HashMap索引将O(n²)复杂度降低到O(n)
//     */
//    private Patch<String> generateOptimizedFileNamePatch(List<String> original, List<String> revised) {
//        Patch<String> patch = new Patch<>();
//
//        // 1. 建立文件名索引 - O(n) 复杂度
//        Map<String, FileLineInfo> originalIndex = buildFilenameIndex(original);
//        Map<String, FileLineInfo> revisedIndex = buildFilenameIndex(revised);
//
//        // 2. 找出共同文件名 - O(n) 复杂度
//        Set<String> commonFiles = new HashSet<>(originalIndex.keySet());
//        commonFiles.retainAll(revisedIndex.keySet());
//
//        // 3. 处理修改的文件 - O(n) 复杂度
//        Set<String> processedOriginal = new HashSet<>();
//        Set<String> processedRevised = new HashSet<>();
//
//        for (String filename : commonFiles) {
//            FileLineInfo origInfo = originalIndex.get(filename);
//            FileLineInfo revInfo = revisedIndex.get(filename);
//
//            if (!origInfo.line.equals(revInfo.line)) {
//                // 文件内容有变化，创建ChangeDelta
//                List<String> origLines = new ArrayList<>();
//                origLines.add(origInfo.line);
//                Chunk<String> origChunk = new Chunk<>(origInfo.index, origLines);
//
//                List<String> revLines = new ArrayList<>();
//                revLines.add(revInfo.line);
//                Chunk<String> revChunk = new Chunk<>(revInfo.index, revLines);
//
//                patch.addDelta(new ChangeDelta<>(origChunk, revChunk));
//            }
//
//            processedOriginal.add(filename);
//            processedRevised.add(filename);
//        }
//
//        // 4. 处理删除的文件 - O(n) 复杂度
//        for (Map.Entry<String, FileLineInfo> entry : originalIndex.entrySet()) {
//            if (!processedOriginal.contains(entry.getKey())) {
//                FileLineInfo info = entry.getValue();
//                List<String> origLines = new ArrayList<>();
//                origLines.add(info.line);
//                Chunk<String> origChunk = new Chunk<>(info.index, origLines);
//                Chunk<String> revChunk = new Chunk<>(info.index, new ArrayList<>());
//                patch.addDelta(new DeleteDelta<>(origChunk, revChunk));
//            }
//        }
//
//        // 5. 处理新增的文件 - O(n) 复杂度
//        for (Map.Entry<String, FileLineInfo> entry : revisedIndex.entrySet()) {
//            if (!processedRevised.contains(entry.getKey())) {
//                FileLineInfo info = entry.getValue();
//                int insertPos = original.size(); // 插入到末尾
//                Chunk<String> origChunk = new Chunk<>(insertPos, new ArrayList<>());
//                List<String> revLines = new ArrayList<>();
//                revLines.add(info.line);
//                Chunk<String> revChunk = new Chunk<>(insertPos, revLines);
//                patch.addDelta(new InsertDelta<>(origChunk, revChunk));
//            }
//        }
//
//        return patch;
//    }
//
//    /**
//     * 构建文件名索引 - 性能优化关键
//     */
//    private Map<String, FileLineInfo> buildFilenameIndex(List<String> lines) {
//        Map<String, FileLineInfo> index = new HashMap<>(lines.size() * 4 / 3); // 预分配容量
//
//        for (int i = 0; i < lines.size(); i++) {
//            String line = lines.get(i);
//            String filename = extractFilename(line);
//            if (filename != null) {
//                index.put(filename, new FileLineInfo(i, line));
//            }
//        }
//
//        return index;
//    }
//
//    // ==================== 字符串处理优化方法 ====================
//
//    /**
//     * 格式验证方法（优化版本）
//     */
//    private boolean isAllLinesValidFormat(List<String> lines) {
//        if (lines == null || lines.isEmpty()) {
//            return true;
//        }
//
//        // 批量验证，提前退出
//        for (String line : lines) {
//            if (line == null || !FORMAT_PATTERN.matcher(line).matches()) {
//                return false;
//            }
//        }
//        return true;
//    }
//
//    /**
//     * 提取文件名方法（优化版本）
//     */
//    private String extractFilename(String line) {
//        if (line == null || line.isEmpty()) {
//            return null;
//        }
//
//        Matcher matcher = FILENAME_PATTERN.matcher(line);
//        return matcher.find() ? matcher.group(1).trim() : null;
//    }
//
//    /**
//     * 优化版本的字符串替换方法
//     * 使用缓存机制避免重复处理
//     */
//    private String getOptimizedReplace(String str) {
//        if (str == null) {
//            return null;
//        }
//
//        // 快速检查：如果不包含<符号，直接返回
//        if (str.indexOf('<') == -1) {
//            return str;
//        }
//
//        // 使用缓存
//        return REPLACE_CACHE.computeIfAbsent(str, s -> {
//            if (REPLACE_CACHE.size() >= MAX_CACHE_SIZE) {
//                // 缓存满了，清理一半
//                REPLACE_CACHE.clear();
//            }
//            return s.contains("<br>") ? s.replace("<br>", "") : s;
//        });
//    }
//
//    /**
//     * 保留原有方法以保持兼容性（已废弃）
//     * @deprecated 使用 getOptimizedReplace 替代
//     */
//    @Deprecated
//    private String getReplace(String str) {
//        return getOptimizedReplace(str);
//    }
//
//    // ==================== 性能监控和测试方法 ====================
//
//    /**
//     * 性能测试主方法
//     * 用于测试优化效果
//     */
//    public static void main(String[] args) throws IOException {
//        CompareSourceTargetContentCompnent component = new CompareSourceTargetContentCompnent();
//
//        // 性能测试
//        performanceTest1(component);
//    }
//
//    /**
//     * 性能测试方法
//     */
//    private static void performanceTest(CompareSourceTargetContentCompnent component) throws IOException {
//        System.out.println("=== 性能测试开始 ===");
//
//        // 测试文件路径（需要根据实际情况调整）
//        String fileA = "D://a.txt";
//        String fileB = "D://b.txt";
//
//        try {
//            String contentA = readFileToString(fileA);
//            String contentB = readFileToString(fileB);
//
//            List<String> aLines = new ArrayList<>(Arrays.asList(contentA.split("\n")));
//            List<String> bLines = new ArrayList<>(Arrays.asList(contentB.split("\n")));
//
//            System.out.println("文件A行数: " + aLines.size());
//            System.out.println("文件B行数: " + bLines.size());
//            System.out.println("预估文件大小: " + (contentA.length() + contentB.length()) / 1024 + " KB");
//
//            // 预热JVM
//            for (int i = 0; i < 3; i++) {
//              Map<String,Object>  map = component.compare(aLines, bLines);
//                System.out.println(JSON.toJSONString(map));
//
//            }
//
//            // 正式测试
//            int testRounds = 5;
//            long totalTime = 0;
//
//            for (int i = 0; i < testRounds; i++) {
//                // 清理缓存以获得更准确的测试结果
//                System.gc();
//                Thread.sleep(100);
//
//                long start = System.currentTimeMillis();
//                Map<String, Object> result = component.compare(aLines, bLines);
//                long end = System.currentTimeMillis();
//
//                long duration = end - start;
//                totalTime += duration;
//
//                System.out.println("第" + (i + 1) + "轮测试耗时: " + duration + " 毫秒");
//                System.out.println("比对结果: " + (Boolean) result.get("ret"));
//            }
//
//            double avgTime = (double) totalTime / testRounds;
//            System.out.println("=== 测试结果 ===");
//            System.out.println("平均耗时: " + String.format("%.2f", avgTime) + " 毫秒");
//            System.out.println("总耗时: " + totalTime + " 毫秒");
//            System.out.println("缓存使用情况:");
//            System.out.println("  - 字符串替换缓存: " + REPLACE_CACHE.size() + " 项");
//            System.out.println("  - HTML片段缓存: " + HTML_FRAGMENT_CACHE.size() + " 项");
//
//        } catch (Exception e) {
//            System.err.println("性能测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    private static void performanceTest1(CompareSourceTargetContentCompnent component) throws IOException {
//        System.out.println("=== 性能测试开始 ===");
//
//        // 测试文件路径（需要根据实际情况调整）
//        String fileA = "D://c1.txt";
//        String fileB = "D://c2.txt";
//
//        try {
//
//            String contentA = readFileToString(fileA);
//            String contentB = readFileToString(fileB);
//
//            List<String> aLines = new ArrayList<>(Arrays.asList(contentA.split("\n")));
//            List<String> bLines = new ArrayList<>(Arrays.asList(contentB.split("\n")));
//            long start = System.currentTimeMillis();
//            System.out.println("文件A行数: " + aLines.size());
//            System.out.println("文件B行数: " + bLines.size());
//            System.out.println("预估文件大小: " + (contentA.length() + contentB.length()) / 1024 + " KB");
//
//            Map<String,Object>  map = component.compare(aLines, bLines);
//            System.out.println(JSON.toJSONString(map));
//            long end = System.currentTimeMillis();
//
//            long duration = end - start;
//            System.out.println("测试耗时: " + duration + " 毫秒");
//
//
//
//
//        } catch (Exception e) {
//            System.err.println("性能测试失败: " + e.getMessage());
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * 清理缓存方法（用于内存管理）
//     */
//    public static void clearCaches() {
//        REPLACE_CACHE.clear();
//        HTML_FRAGMENT_CACHE.clear();
//        System.gc(); // 建议垃圾回收
//    }
//
//    /**
//     * 获取缓存统计信息
//     */
//    public static Map<String, Integer> getCacheStats() {
//        Map<String, Integer> stats = new HashMap<>();
//        stats.put("replaceCacheSize", REPLACE_CACHE.size());
//        stats.put("htmlFragmentCacheSize", HTML_FRAGMENT_CACHE.size());
//        return stats;
//    }
//
//    /**
//     * 优化版本的文件读取方法
//     * @param filePath 文件路径
//     * @return 文件内容字符串
//     * @throws IOException 当读取文件发生错误时抛出
//     */
//    public static String readFileToString(String filePath) throws IOException {
//        // 使用BufferedReader处理大文件，优化内存使用
//        StringBuilder content = new StringBuilder();
//        try (BufferedReader reader = new BufferedReader(
//                new InputStreamReader(new FileInputStream(filePath), StandardCharsets.UTF_8))) {
//            String line;
//            boolean firstLine = true;
//            while ((line = reader.readLine()) != null) {
//                if (!firstLine) {
//                    content.append('\n'); // 保留换行符
//                }
//                content.append(line);
//                firstLine = false;
//            }
//        } catch (FileNotFoundException e) {
//            throw new IOException("文件未找到: " + filePath, e);
//        } catch (IOException e) {
//            throw new IOException("读取文件失败: " + filePath, e);
//        }
//        return content.toString();
//    }
//
//    // ==================== 兼容性和向后兼容方法 ====================
//
//    /**
//     * 向后兼容的比对方法入口
//     * 确保与原有调用方式完全兼容
//     */
//    public Map<String, Object> compareFiles(List<String> aLines, List<String> bLines) {
//        return compare(aLines, bLines);
//    }
//
//    /**
//     * 批量比对方法（新增功能）
//     * 可以同时处理多个文件对的比对
//     */
//    public List<Map<String, Object>> batchCompare(List<List<String>> sourceFiles, List<List<String>> targetFiles) {
//        if (sourceFiles == null || targetFiles == null || sourceFiles.size() != targetFiles.size()) {
//            throw new IllegalArgumentException("源文件列表和目标文件列表不能为空且大小必须相等");
//        }
//
//        List<Map<String, Object>> results = new ArrayList<>(sourceFiles.size());
//        for (int i = 0; i < sourceFiles.size(); i++) {
//            results.add(compare(sourceFiles.get(i), targetFiles.get(i)));
//        }
//
//        return results;
//    }
//
//    /**
//     * 获取优化统计信息
//     * 用于监控优化效果
//     */
//    public Map<String, Object> getOptimizationStats() {
//        Map<String, Object> stats = new HashMap<>();
//        stats.put("replaceCacheSize", REPLACE_CACHE.size());
//        stats.put("htmlFragmentCacheSize", HTML_FRAGMENT_CACHE.size());
//        stats.put("chunkSize", CHUNK_SIZE);
//        stats.put("estimatedRowHtmlSize", ESTIMATED_ROW_HTML_SIZE);
//        stats.put("maxCacheSize", MAX_CACHE_SIZE);
//
//        // 内存使用情况
//        Runtime runtime = Runtime.getRuntime();
//        stats.put("totalMemory", runtime.totalMemory());
//        stats.put("freeMemory", runtime.freeMemory());
//        stats.put("usedMemory", runtime.totalMemory() - runtime.freeMemory());
//
//        return stats;
//    }
//}
