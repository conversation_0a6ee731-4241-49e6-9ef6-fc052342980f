package com.ideal.envc.service.impl;

import com.ideal.envc.service.IFileOperationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Arrays;
import java.util.List;

/**
 * 文件操作使用示例服务
 * 展示如何在业务中使用文件操作服务
 *
 * <AUTHOR>
 */
@Service
public class FileOperationExampleService {

    private static final Logger logger = LoggerFactory.getLogger(FileOperationExampleService.class);

    @Autowired
    private IFileOperationService fileOperationService;

    /**
     * 保存比对结果示例
     * 演示如何保存比对结果到文件
     *
     * @param flowId 流程ID
     * @param resultContent 比对结果内容
     * @return 保存的文件路径，失败返回null
     */
    public String saveComparisonResult(String flowId, String resultContent) {
        logger.info("开始保存比对结果，流程ID：{}", flowId);

        try {
            /* 检查文件存储功能是否启用 */
            if (!fileOperationService.isFileStorageEnabled()) {
                logger.warn("文件存储功能未启用，跳过文件保存");
                return null;
            }

            /* 生成文件路径 */
            String filePath = fileOperationService.generateResultFilePath(flowId);
            logger.info("生成文件路径：{}", filePath);

            /* 保存文件 */
            boolean success = fileOperationService.saveResultFile(resultContent, flowId);
            if (success) {
                logger.info("比对结果保存成功，文件路径：{}", filePath);
                return filePath;
            } else {
                logger.error("比对结果保存失败，流程ID：{}", flowId);
                return null;
            }

        } catch (Exception e) {
            logger.error("保存比对结果异常，流程ID：{}", flowId, e);
            return null;
        }
    }

    /**
     * 读取比对结果示例
     * 演示如何从文件读取比对结果
     *
     * @param filePath 文件路径
     * @return 比对结果内容，失败返回null
     */
    public String loadComparisonResult(String filePath) {
        logger.info("开始读取比对结果，文件路径：{}", filePath);

        try {
            /* 检查文件存储功能是否启用 */
            if (!fileOperationService.isFileStorageEnabled()) {
                logger.warn("文件存储功能未启用，无法读取文件");
                return null;
            }

            /* 读取文件内容 */
            String content = fileOperationService.readResultFile(filePath);
            if (content != null) {
                logger.info("比对结果读取成功，内容长度：{}", content.length());
            } else {
                logger.warn("比对结果读取失败或文件不存在，文件路径：{}", filePath);
            }

            return content;

        } catch (Exception e) {
            logger.error("读取比对结果异常，文件路径：{}", filePath, e);
            return null;
        }
    }

    /**
     * 更新比对结果示例
     * 演示如何更新比对结果文件
     *
     * @param filePath 文件路径
     * @param additionalContent 追加的内容
     * @param isReplace 是否替换原有内容
     * @return true-更新成功，false-更新失败
     */
    public boolean updateComparisonResult(String filePath, String additionalContent, boolean isReplace) {
        logger.info("开始更新比对结果，文件路径：{}，替换模式：{}", filePath, isReplace);

        try {
            /* 检查文件存储功能是否启用 */
            if (!fileOperationService.isFileStorageEnabled()) {
                logger.warn("文件存储功能未启用，无法更新文件");
                return false;
            }

            /* 更新文件内容 */
            boolean success = fileOperationService.updateResultFile(additionalContent, filePath, isReplace);
            if (success) {
                logger.info("比对结果更新成功，文件路径：{}", filePath);
            } else {
                logger.error("比对结果更新失败，文件路径：{}", filePath);
            }

            return success;

        } catch (Exception e) {
            logger.error("更新比对结果异常，文件路径：{}", filePath, e);
            return false;
        }
    }

    /**
     * 清理过期文件示例
     * 演示如何批量删除过期的比对结果文件
     *
     * @param expiredFilePaths 过期文件路径列表
     * @return true-全部清理成功，false-存在清理失败的文件
     */
    public boolean cleanupExpiredResults(List<String> expiredFilePaths) {
        logger.info("开始清理过期比对结果文件，文件数量：{}", expiredFilePaths.size());

        try {
            /* 检查文件存储功能是否启用 */
            if (!fileOperationService.isFileStorageEnabled()) {
                logger.warn("文件存储功能未启用，跳过文件清理");
                return true;
            }

            /* 批量删除文件 */
            boolean success = fileOperationService.batchDeleteResultFiles(expiredFilePaths);
            if (success) {
                logger.info("过期文件清理成功，清理数量：{}", expiredFilePaths.size());
            } else {
                logger.warn("过期文件清理部分失败，请检查日志");
            }

            return success;

        } catch (Exception e) {
            logger.error("清理过期文件异常", e);
            return false;
        }
    }

    /**
     * 获取配置信息示例
     * 演示如何获取文件操作相关的配置信息
     */
    public void printConfigurationInfo() {
        logger.info("========== 文件操作配置信息 ==========");
        
        try {
            /* 检查功能启用状态 */
            boolean enabled = fileOperationService.isFileStorageEnabled();
            logger.info("文件存储功能启用状态：{}", enabled);

            /* 获取基础路径 */
            String basePath = fileOperationService.getBasePath();
            logger.info("文件存储基础路径：{}", basePath);

            /* 生成示例文件路径 */
            if (enabled) {
                String examplePath = fileOperationService.generateResultFilePath("example_flow_001");
                logger.info("示例文件路径：{}", examplePath);
            }

        } catch (Exception e) {
            logger.error("获取配置信息失败", e);
        }

        logger.info("=====================================");
    }

    /**
     * 完整的文件操作流程示例
     * 演示一个完整的文件操作流程：创建 -> 读取 -> 更新 -> 删除
     *
     * @param flowId 流程ID
     * @return true-流程执行成功，false-流程执行失败
     */
    public boolean demonstrateCompleteWorkflow(String flowId) {
        logger.info("开始演示完整的文件操作流程，流程ID：{}", flowId);

        try {
            /* 1. 保存初始内容 */
            String initialContent = "初始比对结果内容 - 流程ID: " + flowId;
            String filePath = saveComparisonResult(flowId, initialContent);
            if (filePath == null) {
                logger.error("保存初始内容失败");
                return false;
            }

            /* 2. 读取文件内容 */
            String readContent = loadComparisonResult(filePath);
            if (readContent == null) {
                logger.error("读取文件内容失败");
                return false;
            }

            /* 3. 追加更新内容 */
            String additionalContent = "\n追加的比对结果内容";
            boolean updateSuccess = updateComparisonResult(filePath, additionalContent, false);
            if (!updateSuccess) {
                logger.error("追加更新内容失败");
                return false;
            }

            /* 4. 清理文件 */
            boolean cleanupSuccess = cleanupExpiredResults(Arrays.asList(filePath));
            if (!cleanupSuccess) {
                logger.error("清理文件失败");
                return false;
            }

            logger.info("完整的文件操作流程演示成功");
            return true;

        } catch (Exception e) {
            logger.error("文件操作流程演示异常，流程ID：{}", flowId, e);
            return false;
        }
    }
}
