package com.ideal.envc.service.impl;

import com.ideal.envc.common.FileOperationUtils;
import com.ideal.envc.config.ContrastResultConfig;
import com.ideal.envc.service.IFileOperationService;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Collection;

/**
 * 文件操作服务实现类
 * 提供基于配置的文件操作功能实现
 *
 * <AUTHOR>
 */
@Service
public class FileOperationServiceImpl implements IFileOperationService {

    private static final Logger logger = LoggerFactory.getLogger(FileOperationServiceImpl.class);

    private final ContrastResultConfig contrastResultConfig;

    public FileOperationServiceImpl(ContrastResultConfig contrastResultConfig) {
        this.contrastResultConfig = contrastResultConfig;
    }

    @Override
    public String generateResultFilePath(String flowId) {
        logger.debug("开始生成比对结果文件路径，流程ID：{}", flowId);

        if (StringUtils.isBlank(flowId)) {
            logger.error("生成比对结果文件路径失败：流程ID不能为空");
            throw new IllegalArgumentException("流程ID不能为空");
        }

        /* 检查文件存储功能是否启用 */
        if (!isFileStorageEnabled()) {
            logger.warn("文件存储功能未启用，无法生成文件路径");
            throw new IllegalStateException("文件存储功能未启用");
        }

        String basePath = getBasePath();
        return FileOperationUtils.generateFilePath(basePath, flowId);
    }

    @Override
    public boolean saveResultFile(String content, String flowId) {
        logger.debug("开始保存比对结果文件，流程ID：{}", flowId);

        /* 检查文件存储功能是否启用 */
        if (!isFileStorageEnabled()) {
            logger.warn("文件存储功能未启用，无法保存文件");
            return false;
        }

        try {
            /* 生成文件路径 */
            String filePath = generateResultFilePath(flowId);

            /* 保存文件 */
            return FileOperationUtils.createFile(content, filePath);

        } catch (Exception e) {
            logger.error("保存比对结果文件失败，流程ID：{}", flowId, e);
            return false;
        }
    }



    @Override
    public String readResultFile(String filePath) {
        logger.debug("开始读取比对结果文件：{}", filePath);

        /* 检查文件存储功能是否启用 */
        if (!isFileStorageEnabled()) {
            logger.warn("文件存储功能未启用，无法读取文件");
            return null;
        }

        return FileOperationUtils.readFile(filePath);
    }

    @Override
    public boolean updateResultFile(String content, String filePath, boolean isReplace) {
        logger.debug("开始更新比对结果文件：{}，替换模式：{}", filePath, isReplace);

        /* 检查文件存储功能是否启用 */
        if (!isFileStorageEnabled()) {
            logger.warn("文件存储功能未启用，无法更新文件");
            return false;
        }

        return FileOperationUtils.updateFile(content, filePath, isReplace);
    }

    @Override
    public boolean updateResultFile(String content, String filePath) {
        return updateResultFile(content, filePath, true);
    }

    @Override
    public boolean deleteResultFile(String filePath) {
        logger.debug("开始删除比对结果文件：{}", filePath);

        /* 检查文件存储功能是否启用 */
        if (!isFileStorageEnabled()) {
            logger.warn("文件存储功能未启用，无法删除文件");
            return false;
        }

        return FileOperationUtils.deleteFile(filePath);
    }

    @Override
    public boolean batchDeleteResultFiles(Collection<String> filePaths) {
        logger.debug("开始批量删除比对结果文件，文件数量：{}", filePaths != null ? filePaths.size() : 0);

        /* 检查文件存储功能是否启用 */
        if (!isFileStorageEnabled()) {
            logger.warn("文件存储功能未启用，无法删除文件");
            return false;
        }

        return FileOperationUtils.batchDeleteFiles(filePaths);
    }

    @Override
    public boolean isFileStorageEnabled() {
        try {
            return contrastResultConfig != null
                    && contrastResultConfig.getModel() != null
                    && contrastResultConfig.getModel().getFile() != null
                    && Boolean.TRUE.equals(contrastResultConfig.getModel().getFile().getEnabled());
        } catch (Exception e) {
            logger.error("检查文件存储功能启用状态失败", e);
            return false;
        }
    }

    @Override
    public String getBasePath() {
        try {
            if (contrastResultConfig != null
                    && contrastResultConfig.getModel() != null
                    && contrastResultConfig.getModel().getFile() != null) {
                String basePath = contrastResultConfig.getModel().getFile().getBasePath();
                return StringUtils.isNotBlank(basePath) ? basePath : "/opt";
            }
        } catch (Exception e) {
            logger.error("获取基础路径失败", e);
        }

        /* 返回默认路径 */
        return "/opt";
    }
}
