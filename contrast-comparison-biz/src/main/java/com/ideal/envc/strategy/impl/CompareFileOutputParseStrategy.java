package com.ideal.envc.strategy.impl;

import com.alibaba.fastjson2.JSON;
import com.ideal.envc.compare.CompareSourceTargetContentManagerV2;
import com.ideal.envc.model.bean.EngineActOutputBean;
import com.ideal.envc.model.dto.ContentCustomDto;
import com.ideal.envc.model.dto.EngineCompareOutPutDto;
import com.ideal.envc.model.dto.OutputParseResult;
import com.ideal.envc.strategy.OutputParseStrategy;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * 比对模式-文件类型输出解析策略
 * <AUTHOR>
 */
@Component
public class CompareFileOutputParseStrategy implements OutputParseStrategy {
    
    private static final Logger logger = LoggerFactory.getLogger(CompareFileOutputParseStrategy.class);
    
    @Override
    public OutputParseResult parse(List<EngineActOutputBean> actOutputs,String actDefName) {
        if (actOutputs == null || actOutputs.isEmpty()) {
            return new OutputParseResult(false, "");
        }
        
        EngineActOutputBean engineActOutputBean = actOutputs.get(0);
        try {
            EngineCompareOutPutDto engineCompareOutPutDto = JSON.parseObject(engineActOutputBean.getOutput(), EngineCompareOutPutDto.class);
            ContentCustomDto contentCustomDto = new ContentCustomDto();

            contentCustomDto.setSourceContent(engineCompareOutPutDto.getSourceContent());
            contentCustomDto.setTargetContent(engineCompareOutPutDto.getTargetContent());

            //增加结果比对逻辑；
            Map<String, Object> map = CompareSourceTargetContentManagerV2.getInstance().compare(contentCustomDto.getSourceContent(), contentCustomDto.getTargetContent());
            contentCustomDto.setContent(map.get(CompareSourceTargetContentManagerV2.COMPARERESULT)!=null?map.get(CompareSourceTargetContentManagerV2.COMPARERESULT).toString():"");
            contentCustomDto.setRet(map.get(CompareSourceTargetContentManagerV2.RET) != null && Boolean.parseBoolean(map.get(CompareSourceTargetContentManagerV2.RET).toString()));


            return new OutputParseResult(contentCustomDto.isRet(), JSON.toJSONString(contentCustomDto));
        } catch (Exception e) {
            logger.error("解析engineActOutputBean异常", e);
            return new OutputParseResult(false, "");
        }
    }
    
    @Override
    public String getType() {
        return "COMPARE_FILE";
    }
} 